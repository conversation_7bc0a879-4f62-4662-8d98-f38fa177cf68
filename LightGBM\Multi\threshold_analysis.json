{"analysis_date": "2025-10-01T08:25:14.610820", "total_models_analyzed": 6, "current_thresholds": {"min_accuracy": 0.45, "min_auc": 0.48, "min_f1": 0.08, "min_precision": 0.08, "min_recall": 0.3, "min_win_rate": 0.35, "min_expectancy": 10.0, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "recommended_thresholds": {"accuracy": {"threshold": 0.9039491299214186, "mean": 0.9524629151633058, "std": 0.05467492039708957, "min": 0.8751218153974663, "max": 0.9919294574801972, "count": 6}, "auc": {"threshold": 0.8658208174756622, "mean": 0.8675404755757358, "std": 0.0047031497629068135, "min": 0.8583894138659529, "max": 0.8718346101912121, "count": 6}, "f1": {"threshold": 0.8694294749911335, "mean": 0.9339490224558643, "std": 0.07470496104745357, "min": 0.8259501820045969, "max": 0.9879849600877566, "count": 6}, "precision": {"threshold": 0.8595449613931583, "mean": 0.9241684411494574, "std": 0.08267849209487946, "min": 0.796192418139493, "max": 0.984071709741396, "count": 6}, "recall": {"threshold": 0.9039491299214186, "mean": 0.9524629151633058, "std": 0.05467492039708957, "min": 0.8751218153974663, "max": 0.9919294574801972, "count": 6}, "win_rate": {"threshold": 0.65, "mean": 0.65, "std": 0.0, "min": 0.65, "max": 0.65, "count": 6}, "expectancy": {"threshold": 44.491771398277066, "mean": 47.259257403145234, "std": 3.129015281939344, "min": 42.82245946315151, "max": 49.520484463649666, "count": 6}}, "new_thresholds": {"min_accuracy": 0.9039491299214186, "min_auc": 0.8658208174756622, "min_f1": 0.8694294749911335, "min_precision": 0.8595449613931583, "min_recall": 0.9039491299214186, "min_win_rate": 0.006500000000000001, "min_expectancy": 44.491771398277066, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.8751218153974663, "auc": 0.8583894138659529, "f1": 0.8259501820045969, "precision": 0.796192418139493, "recall": 0.8751218153974663}], "trading_stats": [{"win_rate": 0.65, "expectancy": 42.82245946315151, "num_trades": 0}]}, "GOLD_M60_counter_trend": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.8751691983756956, "auc": 0.8652171330532458, "f1": 0.8307003637951184, "precision": 0.8190637581785822, "recall": 0.8751691983756956}], "trading_stats": [{"win_rate": 0.65, "expectancy": 42.84641603322072, "num_trades": 0}]}, "GOLD_M60_trend_following_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Buy", "metrics": [{"accuracy": 0.9905058596647381, "auc": 0.871041097236783, "f1": 0.9857814316562016, "precision": 0.981101858030182, "recall": 0.9905058596647381}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.42783749344611, "num_trades": 0}]}, "GOLD_M60_counter_trend_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Buy", "metrics": [{"accuracy": 0.9902889245585875, "auc": 0.8718346101912121, "f1": 0.9856168085791789, "precision": 0.9809885710368862, "recall": 0.9902889245585875}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.43223226832921, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9919294574801972, "auc": 0.8676318707429115, "f1": 0.9879849600877566, "precision": 0.984071709741396, "recall": 0.9919294574801972}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.520484463649666, "num_trades": 0}]}, "GOLD_M60_counter_trend_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Sell", "metrics": [{"accuracy": 0.9917622355031497, "auc": 0.8711287283643099, "f1": 0.987660388612333, "precision": 0.983592331770205, "recall": 0.9917622355031497}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.50611469707418, "num_trades": 0}]}}}