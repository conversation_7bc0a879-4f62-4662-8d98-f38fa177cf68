
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-10-01 09:10:20
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
2025-10-01 09:10:20 | INFO     | FinancialAnalysis | 🚀 Enhanced Logging System เริ่มทำงาน
2025-10-01 09:10:20 | INFO     | FinancialAnalysis | 🏗️ เปิดใช้งาน Financial Analysis System | base_currency=USD | leverage=500
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 09:10:20
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ GBPUSD M60
✅ สามารถเทรนได้: allowed

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ GBPUSD M60...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: GBPUSD_M60 specific

🔸 GBPUSD_M60
   Score: 57.87
   Win Rate: 50.0%
   Total Profit: $73
   Total Trades: 28
   Expectancy: 2.60
   Max Drawdown: $62
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 25
     Volume Spike: 1.0
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 (unchanged)
   input_take_profit: 2.0 (unchanged)
   input_rsi_level_in: 35 → 25
   input_volume_spike: 1.25 → 1.0
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GBPUSD_H1_FIXED.csv main_round 1 symbol GBPUSD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GBPUSD MM60, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GBPUSD MM60, ใช้ค่า default: 4

✅ ข้อมูลที่ส่งเข้า create_features : nBars_SL 4

🏗️ เปิดใช้งาน load and clean data
--- Loading and Cleaning GBPUSD_H1_FIXED.csv ---
✅ อ่านไฟล์สำเร็จด้วย : CSV_Files_Fixed/GBPUSD_H1_FIXED.csv
🔍 ตรวจสอบโครงสร้างไฟล์: CSV_Files_Fixed/GBPUSD_H1_FIXED.csv
📊 จำนวนคอลัมน์: 7
📊 Shape: (74555, 7)
📊 คอลัมน์ปัจจุบัน: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_01_Load_Clean.csv

🏗️ เปิดใช้งาน create features
🔍 ใช้ระบบป้องกัน Data Leakage
🔍 สร้าง safe features...
📊 คอลัมน์ที่มีอยู่: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime']
⚠️ ไม่พบ RSI14 - ข้าม
⚠️ ไม่พบ MACD_12_26_9 - ข้าม
⚠️ ไม่พบ MACDs_12_26_9 - ข้าม
⚠️ ไม่พบ ATR - ข้าม
⚠️ ไม่พบ EMA50 - ข้าม
⚠️ ไม่พบ EMA100 - ข้าม
⚠️ ไม่พบ EMA200 - ข้าม
📊 สร้าง safe features: 4 features
📊 ลบ dangerous features: 0 features
✅ สร้าง safe features เสร็จสิ้น (74555 rows)
✅ สร้าง safe features เสร็จสิ้น
ตรวจสอบค่า : timeframe M60 timeframe_int 60
 Save_Data True Print True

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_02a_features.csv
🔍 ตรวจสอบ columns ข้อมูล df ก่อนเข้า combined : จำนวน 147
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'Volume_MA20_safe', 'Close_MA5_safe', 'Close_MA10_safe', 'Close_MA20_safe', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_OP5', 'EMA_OP10', 'EMA_OP15', 'EMA_CL5', 'EMA_CL10', 'EMA_CL15', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_EMA4', 'RSI_EMA8', 'RSI_EMA12', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'EMA12', 'EMA26', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'Support_100', 'Resistance_100', 'PullBack_100_Up', 'PullBack_100_Down', 'SL_Buy', 'SL_Sell', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_02b_combined.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง combined : จำนวน 242
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

--- Merging MTF features ---
🔍 ตรวจสอบ columns ข้อมูล base_df ก่อนเข้า MTF features : จำนวน 241
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🏗️ เปิดใช้งาน create resampled df
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_03b_MTF_D1.csv
🏗️ เปิดใช้งาน create resampled df
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_03b_MTF_H12.csv
🏗️ เปิดใช้งาน create resampled df
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_03b_MTF_H8.csv
🏗️ เปิดใช้งาน create resampled df
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_03b_MTF_H4.csv
🏗️ เปิดใช้งาน create resampled df
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_03b_MTF_H2.csv
🏗️ เปิดใช้งาน create features mtf
✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_04_D1_Features.csv
🏗️ เปิดใช้งาน create features mtf
✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_04_H12_Features.csv
🏗️ เปิดใช้งาน create features mtf
✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_04_H8_Features.csv
🏗️ เปิดใช้งาน create features mtf
✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_04_H4_Features.csv
🏗️ เปิดใช้งาน create features mtf
✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_04_H2_Features.csv

Last base_df: 2025-07-11 23:00:00
Last H2: 2025-07-11 22:00:00
Last H4: 2025-07-11 20:00:00
Last D1: 2025-07-11 00:00:00

ตรวจสอบว่า row สุดท้ายยังอยู่หลัง merge:

Last row before merge (df_combined) :              Date      Time     Open     High      Low    Close  Volume            DateTime  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
74554  2025.07.11  23:00:00  1.34968  1.34971  1.34838  1.34886    1365 2025-07-11 23:00:00  ...     0.000614       2545.4     1.350597       0.00096        3294.0     1.352796      0.002472       3245.85

[1 rows x 242 columns]

Last row before merge (base_df) :                            Date      Time     Open     High      Low    Close  Volume  DayOfWeek  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
DateTime                                                                                          ...                                                                                                            
2025-07-11 23:00:00  2025.07.11  23:00:00  1.34968  1.34971  1.34838  1.34886    1365          4  ...     0.000614       2545.4     1.350597       0.00096        3294.0     1.352796      0.002472       3245.85

[1 rows x 241 columns]

Last row after merge (df_merged) :                            Date      Time     Open     High      Low    Close  Volume  ...  D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
DateTime                                                                               ...                                                                                                                             
2025-07-11 23:00:00  2025.07.11  23:00:00  1.34968  1.34971  1.34838  1.34886    1365  ...                0.0              0.0                -1.0                     -1.0           1.0          -1.0            -1.0

[1 rows x 331 columns]

✅ Final df_merged columns: 332
Index(['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime',
       'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight',
       'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB'],
      dtype='object')
🔎 Last row:                               74554
Date                     2025.07.11
Time                       23:00:00
Open                        1.34968
High                        1.34971
Low                         1.34838
...                             ...
D1_Volume_Momentum             -1.0
D1_Volume_TrendStrength        -1.0
D1_MACD_line                    1.0
D1_MACD_deep                   -1.0
D1_MACD_signal                 -1.0

[332 rows x 1 columns]

✅ Saved final merged features to: LightGBM/Data_Trained\M60_GBPUSD_05a_df_merged.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง mtf : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 204 จาก 74555 แถว)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_05b_df_dropna.csv

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2013-07-18 13:00:00 ถึง 2025-07-11 23:00:00
- ระยะเวลารวม: 4376 days 10:00:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 01:24:45.708137188
- ช่วงห่างระหว่างบันทึก (สูงสุด): 3 days 06:00:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 01:00:00
- จำนวนช่วงเวลาที่หายไป: 656 (จากทั้งหมด 74350 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: -1.8625
p-value: 0.3499
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -33.9931
p-value: 0.0000
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

💾 บันทึกรายงาน Temporal Analysis ที่: LightGBM/Multi/results\M60_GBPUSD_temporal_report.json

📌 จำนวน Missing Values แสดงเฉพาะคอลัมน์ที่มีค่าว่าง และจำนวน > 0:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)

🔍 Unique values in df['DayOfWeek']: [3 4 0 1 2 6]

🏗️ เปิดใช้งาน check data quality
==================================================
Data Quality Check for CSV_Files_Fixed/GBPUSD_H1_FIXED.csv
==================================================

[4] Duplicate Rows: 0
💾 บันทึก df_with_features ลงไฟล์ LightGBM/Data_Trained/M60_GBPUSD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GBPUSD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M60_GBPUSD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GBPUSD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GBPUSD MM60
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
❌ counter_trend: ไม่พบไฟล์โมเดล
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 0/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
❌ ไม่พบโมเดลใดเลยสำหรับ GBPUSD MM60
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ GBPUSD_M60_technical
   ค่า threshold: 0.2
   จำนวน trades: 9110
   ⏱️ ข้อมูลบันทึกเมื่อ 2 วัน 19 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2000

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

📊 ใช้ Single-Model Architecture
ตรวจสอบการใช้ Model ML : False
model_features: None

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 204 (ข้อมูลทั้งหมด 74351 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 74147
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ GBPUSD M60
🔄 กำลังประมวลผล GBPUSD M60...
2025-10-01 09:16:14 | INFO     | FinancialAnalysis | 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GBPUSD | timeframe=M60 | trades_count=16112
2025-10-01 09:16:16 | INFO     | FinancialAnalysis | 💰 Financial Analysis: GBPUSD_M60 | symbol=GBPUSD | timeframe=M60 | total_trades=16112 | total_profit_usd=-333029.*********** | max_drawdown_usd=334414.*********** | margin_per_trade=252.99999999999997
💾 บันทึกการวิเคราะห์ GBPUSD M60 ที่: Financial_Analysis_Results/GBPUSD_M60_financial_analysis.json
✅ ประมวลผล GBPUSD M60 สำเร็จ: 16112 รายการ
✅ วิเคราะห์ทางการเงิน GBPUSD M60 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 16112 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2000 (Technical Analysis)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_06a_trade_df.csv

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 74351 ตัวอย่างข้อมูล df
           Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
204  2013.07.18  13:00:00  1.52186  1.52304  1.52052  1.52178    2246  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
205  2013.07.18  14:00:00  1.52178  1.52209  1.51857  1.51912    3463  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
206  2013.07.18  15:00:00  1.51909  1.52168  1.51863  1.52072    2836  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
207  2013.07.18  16:00:00  1.52072  1.52126  1.51820  1.51999    3477  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
208  2013.07.18  17:00:00  1.51995  1.52075  1.51907  1.51912    2262  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 16112 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-07-31 06:00:00      1.52238 2013-07-31 09:00:00     1.52238       Sell     0.0          2  ...      0.001464           0.013606       27.793491  0.000000    0.002266               1660.35                      0
1 2013-07-31 11:00:00      1.52118 2013-07-31 13:00:00     1.52118       Sell     0.0          2  ...      0.001459           0.005712       31.619001  0.000000    0.002255               1593.65                      1
2 2013-07-31 15:00:00      1.51504 2013-07-31 15:00:00     1.51504       Sell     0.0          2  ...      0.002271           0.008535       23.734541  0.000000    0.003346               1703.55                      1
3 2013-07-31 16:00:00      1.51342 2013-07-31 16:00:00     1.51602       Sell  -260.0          2  ...      0.002345           0.011626       21.567285  0.001718    0.003443               1772.85                      1
4 2013-07-31 19:00:00      1.51758 2013-07-31 19:00:00     1.51926       Sell  -168.0          2  ...      0.002694           0.012540       38.072840  0.001107    0.002221               2061.00                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                24.23               
Expectancy          -41.54              
📈 สถิติสำหรับ Sell Trades:
Win%                26.00               
Expectancy          -35.06              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                25.09               
Expectancy          -38.40              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    13.29          3009                
Tuesday   14.37          3319                
Wednesday 12.66          3287                
Thursday  13.87          3339                
Friday    13.30          3158                
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         12.87          1072                
5         12.38          711                 
6         16.35          526                 
7         13.10          435                 
8         14.15          424                 
9         16.70          557                 
10        12.99          970                 
11        13.81          1137                
12        12.47          1227                
13        14.38          1113                
14        13.31          1044                
15        14.83          1045                
16        13.68          1162                
17        16.25          1163                
18        9.91           1160                
19        13.68          936                 
20        12.91          728                 
21        11.25          702                 
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-07-31 06:00:00
1   2013-07-31 11:00:00
2   2013-07-31 15:00:00
3   2013-07-31 16:00:00
4   2013-07-31 19:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-07-31 05:55:00
1   2013-07-31 10:55:00
2   2013-07-31 14:55:00
3   2013-07-31 15:55:00
4   2013-07-31 18:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 204   2013-07-18 13:00:00
205   2013-07-18 14:00:00
206   2013-07-18 15:00:00
207   2013-07-18 16:00:00
208   2013-07-18 17:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 16112
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-07-31 06:00:00      1.52238 2013-07-31 09:00:00     1.52238       Sell     0.0          2           6
1 2013-07-31 11:00:00      1.52118 2013-07-31 13:00:00     1.52118       Sell     0.0          2          11
2 2013-07-31 15:00:00      1.51504 2013-07-31 15:00:00     1.51504       Sell     0.0          2          15
3 2013-07-31 16:00:00      1.51342 2013-07-31 16:00:00     1.51602       Sell  -260.0          2          16
4 2013-07-31 19:00:00      1.51758 2013-07-31 19:00:00     1.51926       Sell  -168.0          2          19

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_06b_trade_df_merge.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 16112/16112 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 1086 samples (6.7%)
  Class 1 (weak_sell): 3557 samples (22.1%)
  Class 2 (no_trade): 6485 samples (40.2%)
  Class 3 (weak_buy): 3908 samples (24.3%)
  Class 4 (strong_buy): 1076 samples (6.7%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0    1086
1    3557
2    6485
3    3908
4    1076
Name: count, dtype: int64
Class 0 (strong_sell): 1086 trades, Profit range: 91.0 to 5597.0
Class 1 (weak_sell): 3557 trades, Profit range: -20.0 to 54.0
Class 2 (no_trade): 6485 trades, Profit range: -1500.0 to -21.0
Class 3 (weak_buy): 3908 trades, Profit range: -16.0 to 60.0
Class 4 (strong_buy): 1076 trades, Profit range: 72.0 to 1272.0

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_07_trade_df_valid_trades.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    13951
1     2161
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    7289
1    1075
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    6662
1    1086
Name: count, dtype: int64

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_08a_trade_df_target.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 16112
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time     EMA50    EMA100    EMA200      RSI14
0 2013-07-31 06:00:00  1.529885  1.531859  1.529394  27.793491
1 2013-07-31 11:00:00  1.528468  1.530929  1.529037  31.619001
2 2013-07-31 15:00:00  1.527083  1.530023  1.528650  23.734541
3 2013-07-31 16:00:00  1.526546  1.529693  1.528499  21.567285
4 2013-07-31 19:00:00  1.525513  1.528977  1.528170  38.072840

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    13951
1     2161
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target               1.000000
D1_Bar_TL            0.019274
Rolling_Close_15     0.019178
RSI14_x_Volume       0.018815
Volume_Change_3      0.018202
                       ...   
H8_Price_Strangth    0.000013
Volume_Lag_1         0.000008
RSI_Divergence_i4         NaN
RSI_Divergence_i2         NaN
RSI_Divergence_i6         NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
                  feature         VIF
0               D1_Bar_TL    1.022112
1        Rolling_Close_15   12.013011
2          RSI14_x_Volume    2.547164
3         Volume_Change_3    1.353907
4              H8_Bar_DTB    1.033951
5                  Bar_CL         inf
6            Slope_EMA200         inf
7         Volume_Change_2    1.021550
8              ATR_ROC_i2    5.707596
9            RSI_Oversold    2.856718
10    EMA50_x_RollingVol5   53.193704
11            RSI_counter         inf
12              RSI_trend         inf
13        D1_Bar_longwick    1.075969
14            Price_Range    3.185678
15          Volume_Lag_50    1.956973
16               ATR_Deep    2.504260
17  ADX_14_x_RollingVol15    5.000344
18        Rolling_Close_5   56.466771
19        RSI_signal_EMA4    3.954221
20       RSI_signal_EMA12    5.364538
21              ATR_Lag_3   46.625481
22                IsNight    1.245327
23           Slope_EMA100    4.620857
24              ATR_Lag_2  152.318073
25              ATR_Lag_1   96.803416
26             H12_Bar_SW    1.742773
27            H12_Bar_DTB    1.032509
28       EMA_diff_100_200         inf
29        RSI_signal_EMA8    6.711023
30            Dist_EMA200  853.690690
31          H8_Price_Move    1.841808
32       EMA_diff_x_RSI14   18.217539
33              Bar_CL_OC    6.401069
34           H4_MACD_deep    2.279713
35        H4_Bar_longwick    1.071230
36           H2_MACD_line    3.386834
37              H4_Bar_SW    1.614840
38        EMA_diff_50_200         inf
39             H4_Bar_DTB    1.006947
40            ADX_zone_15    1.132420
41      PullBack_100_Down   20.649733
42        PullBack_100_Up   19.694270
43        Volume_Momentum    1.357761
44        MA_Cross_50_100    4.017032
45         H8_MACD_signal    2.660450
46             H4_Bar_OSB    1.147969
47        MA_Cross_50_200    5.388995
48            H12_Bar_OSB    1.112319
49                 Bar_SW    2.650322
50                   Hour    3.014663
51           H8_MACD_line    4.175892
52             RSI_ROC_i4    3.052194
53                 Bar_TL    1.067142
54         H4_Price_Range    3.080436
55        EMA_diff_50_100         inf
56     H8_Volume_Momentum    2.234655
57            Dist_EMA100  488.447905
58            IsAfternoon    1.775891

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['Rolling_Close_15', 'Bar_CL', 'Slope_EMA200', 'EMA50_x_RollingVol5', 'RSI_counter', 'RSI_trend', 'Rolling_Close_5', 'ATR_Lag_3', 'ATR_Lag_2', 'ATR_Lag_1', 'EMA_diff_100_200', 'Dist_EMA200', 'EMA_diff_x_RSI14', 'EMA_diff_50_200', 'PullBack_100_Down', 'PullBack_100_Up', 'EMA_diff_50_100', 'Dist_EMA100']

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003799 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3158
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 41
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003467 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3155
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 40
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003426 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3152
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 39
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003045 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3150
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 38
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001045 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3148
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 37
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003176 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3145
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 36
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003319 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3142
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 35
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001257 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3140
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 34
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000859 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3137
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 33
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001009 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3135
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 32
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001141 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3132
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 31
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000869 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3129
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 30
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004226 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3126
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 29
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000831 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3123
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 28
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980

✅ RFE เลือก 28 features จากทั้งหมด 41 features
📋 Top 10 features จาก RFE:
1. D1_Bar_TL
2. RSI14_x_Volume
3. Volume_Change_3
4. Volume_Change_2
5. ATR_ROC_i2
6. D1_Bar_longwick
7. Price_Range
8. Volume_Lag_50
9. ADX_14_x_RollingVol15
10. RSI_signal_EMA4
[LightGBM] [Info] Number of positive: 2161, number of negative: 13951
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003611 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3158
[LightGBM] [Info] Number of data points in the train set: 16112, number of used features: 41
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134124 -> initscore=-1.864980
[LightGBM] [Info] Start training from score -1.864980

✅ Feature Importance เลือก 13 features จากทั้งหมด 41 features
📋 Top 10 features จาก Feature Importance:
1. RSI14_x_Volume
2. Volume_Change_3
3. Volume_Change_2
4. ATR_ROC_i2
5. D1_Bar_longwick
6. Price_Range
7. Volume_Lag_50
8. ADX_14_x_RollingVol15
9. H8_Price_Move
10. H4_Bar_longwick

✅ รวมทั้งสองวิธีได้ 28 features ที่สำคัญ

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 33

✅ Final selected features for training: 33 features
📋 Top 10 features:
1. RSI_signal_EMA4 (corr: 0.0147)
2. H8_Price_Move (corr: 0.0125)
3. H12_Bar_SW (corr: 0.0130)
4. Volume_Momentum (corr: 0.0119)
5. MA_Cross_50_100 (corr: 0.0118)
6. Price_Range (corr: 0.0155)
7. ADX_14_x_RollingVol15 (corr: 0.0150)
8. Volume_Change_3 (corr: 0.0182)
9. ATR_ROC_i2 (corr: 0.0166)
10. Bar_SW (corr: 0.0110)
... และอีก 23 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GBPUSD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GBPUSD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 33
1. RSI_signal_EMA4
2. H8_Price_Move
3. H12_Bar_SW
4. Volume_Momentum
5. MA_Cross_50_100
6. Price_Range
7. ADX_14_x_RollingVol15
8. Volume_Change_3
9. ATR_ROC_i2
10. Bar_SW
11. Slope_EMA100
12. D1_Bar_longwick
13. Volume_Lag_50
14. H4_Bar_OSB
15. D1_Bar_TL
... และอีก 18 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.865876
1    0.134124
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.15
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                         count           mean            std         min           25%            50%            75%           max
RSI_signal_EMA4        16112.0       0.035129       0.999414   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H8_Price_Move          16112.0       0.000152       0.004083   -0.147820     -0.001480       0.000265       0.001900  3.470000e-02
H12_Bar_SW             16112.0       0.004345       0.732129   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
Volume_Momentum        16112.0       0.063741       0.989283   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
MA_Cross_50_100        16112.0       0.023957       0.999744   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
Price_Range            16112.0       0.002701       0.002144    0.000060      0.001450       0.002220       0.003330  9.566000e-02
ADX_14_x_RollingVol15  16112.0       0.026207       0.025469    0.001861      0.013189       0.020494       0.031808  8.082250e-01
Volume_Change_3        16112.0       0.760948       2.393633   -1.000000     -0.156468       0.222344       1.000488  8.275926e+01
ATR_ROC_i2             16112.0       0.051870       0.136449   -0.781339     -0.023736       0.073708       0.136512  6.132295e-01
Bar_SW                 16112.0       0.017378       0.687688   -1.000000      0.000000       0.000000       0.000000  1.000000e+00
Slope_EMA100           16112.0       0.031529       0.999534   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
D1_Bar_longwick        16112.0      -0.178937       1.387509  -15.915493     -0.741538      -0.206564       0.521898  6.710280e+00
Volume_Lag_50          16112.0    2984.649454    2282.765341    0.000000   1391.000000    2472.000000    3890.250000  5.564200e+04
H4_Bar_OSB             16112.0       0.006393       0.442620   -1.000000      0.000000       0.000000       0.000000  1.000000e+00
D1_Bar_TL              16112.0       0.166398       0.372449    0.000000      0.000000       0.000000       0.000000  1.000000e+00
RSI_ROC_i4             16112.0      -0.035062       0.261912   -2.877162     -0.135228       0.007495       0.124976  6.732853e-01
Hour                   16112.0      12.064734       4.895732    0.000000      9.000000      12.000000      16.000000  2.300000e+01
MA_Cross_50_200        16112.0       0.023337       0.999759   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
RSI_signal_EMA12       16112.0       0.022219       0.999784   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
Bar_TL                 16112.0       0.206306       0.404665    0.000000      0.000000       0.000000       0.000000  1.000000e+00
H4_Price_Range         16112.0       0.004222       0.003165    0.000060      0.002240       0.003550       0.005360  1.354100e-01
Bar_CL_OC              16112.0       0.032212       0.825629   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
H4_Bar_longwick        16112.0      -0.043630       2.128433 -111.500000     -0.650602      -0.038348       0.620807  3.860000e+01
Volume_Change_2        16112.0       0.428576       5.760297   -1.000000     -0.178325       0.099186       0.619956  7.190000e+02
H8_MACD_signal         16112.0       0.008813       0.999992   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H4_MACD_deep           16112.0       0.021723       0.999795   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H4_Bar_SW              16112.0       0.023150       0.726711   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
RSI14_x_Volume         16112.0  171846.355056  137468.323129    0.000000  82632.289302  135984.879967  221771.981519  4.305813e+06
DayOfWeek              16112.0       2.019364       1.394456    0.000000      1.000000       2.000000       3.000000  4.000000e+00
IsMorning              16112.0       0.241000       0.427704    0.000000      0.000000       0.000000       0.000000  1.000000e+00
IsAfternoon            16112.0       0.270854       0.444415    0.000000      0.000000       0.000000       1.000000  1.000000e+00
IsEvening              16112.0       0.247704       0.431692    0.000000      0.000000       0.000000       0.000000  1.000000e+00
IsNight                16112.0       0.110539       0.313570    0.000000      0.000000       0.000000       0.000000  1.000000e+00

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
2    6485
3    3908
1    3557
0    1086
4    1076
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 3557
📊 Total negative samples: 1086
📊 Positive distribution - Train: 2135, Val: 711, Test: 711

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 2787 samples, positive: 2135 (76.6%)
Val: 928 samples, positive: 711 (76.6%)
Test: 928 samples, positive: 711 (76.6%)
Train distribution: Target_Multiclass
1    0.766057
0    0.233943
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.766164
0    0.233836
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.766164
0    0.233836
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338
✅ บันทึกไฟล์ train, val, test เรียบร้อย

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 74.734
⚠️ ไม่มีวันใดผ่านเกณฑ์เข้มงวด - เลือกวันที่ดีที่สุด 3 วัน
⚠️ ไม่มีชั่วโมงใดผ่านเกณฑ์เข้มงวด - เลือกชั่วโมงที่ดีที่สุด 8 ชั่วโมง

📊 Enhanced Time Filter Analysis for GBPUSD:
📅 Recommended Days: ['Thursday', 'Tuesday', 'Friday']
⏰ Recommended Hours: [5, 8, 12, 13, 16, 18, 19, 20]
🕐 Continuous Time Blocks: ['12:00-13:59', '18:00-20:59']
📈 Performance Improvement: {'win_rate_improvement': 0.0, 'expectancy_improvement': 41.420705424725796, 'filtered_trades': 246, 'total_trades': 928, 'coverage': 0.2650862068965517, 'filtered_win_rate': 0.0, 'all_win_rate': 0.0}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GBPUSD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
🔍 ตรวจสอบข้อมูลก่อน Feature Scaling...
X_train shape: (2787, 33)
🔍 ตรวจสอบ infinity และ extreme values...
🔍 ตรวจสอบสุดท้าย: infinity=False, NaN=False, extreme=True
⚠️ ยังพบปัญหา - ใช้การแก้ไขแบบเข้มงวดสุด
✅ ใช้การแก้ไขแบบเข้มงวดสุด (แทนที่ด้วย 0 และ clip ค่า)
📊 ข้อมูลสุดท้าย: X_train shape=(2787, 33), min=-47.67, max=1000000.00
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (      RSI_signal_EMA4  H8_Price_Move  H12_Bar_SW  Volume_Momentum  MA_Cross_50_100  Price_Range  ADX_14_x_RollingVol15  ...  H4_Bar_SW  RSI14_x_Volume  DayOfWeek  IsMorning  IsAfternoon  IsEvening   IsNight
0           -0.635634      -0.661747   -1.077398         0.952832        -0.417095    -0.569224               0.187881  ...  -1.339223       -1.012322   0.009013  -0.589643    -0.621745  -0.565886 -0.341448
1            1.573233      -0.069500   -1.077398        -1.075849        -0.417095     0.031144               0.025592  ...  -1.339223       -0.397441   0.009013   1.695941    -0.621745  -0.565886 -0.341448
2           -0.635634      -0.069500   -1.077398         0.952832        -0.417095     1.766990               1.043502  ...  -1.339223        0.199741   0.009013  -0.589643     1.608376  -0.565886 -0.341448
5           -0.635634       1.438039    0.347037         0.952832        -0.417095    -0.370189               1.060323  ...   1.440974       -0.560988   0.726720  -0.589643    -0.621745  -0.565886  2.928706
6            1.573233      -0.709376    0.347037        -1.075849        -0.417095     0.073561               0.144651  ...  -1.339223       -0.103730   0.726720   1.695941    -0.621745  -0.565886 -0.341448
...               ...            ...         ...              ...              ...          ...                    ...  ...        ...             ...        ...        ...          ...        ...       ...
9574         1.573233      -0.052933    1.771471        -1.075849        -0.417095    -0.471338              -0.372902  ...   1.440974       -0.266810   0.726720  -0.589643     1.608376  -0.565886 -0.341448
9576        -0.635634      -0.317995    1.771471        -1.075849        -0.417095     0.693506               0.246767  ...  -1.339223        0.100356   0.726720  -0.589643    -0.621745   1.767139 -0.341448
9577        -0.635634      -0.317995    1.771471        -1.075849        -0.417095     1.773516               0.864112  ...  -1.339223       -0.379096   0.726720  -0.589643    -0.621745   1.767139 -0.341448
9589        -0.635634       1.282730    0.347037        -1.075849        -0.417095    -0.118948              -0.289440  ...   1.440974       -0.196218  -1.426401  -0.589643    -0.621745   1.767139 -0.341448
9592         1.573233       0.574518    0.347037         0.952832        -0.417095    -0.366926              -0.539963  ...   1.440974        0.025984  -0.708694  -0.589643     1.608376  -0.565886 -0.341448

[2787 rows x 33 columns], 0       1
1       1
2       1
5       1
6       1
       ..
9574    0
9576    0
9577    0
9589    0
9592    0
Name: Target_Multiclass, Length: 2787, dtype: int32)
val_data : (       RSI_signal_EMA4  H8_Price_Move  H12_Bar_SW  Volume_Momentum  MA_Cross_50_100  Price_Range  ADX_14_x_RollingVol15  ...  H4_Bar_SW  RSI14_x_Volume  DayOfWeek  IsMorning  IsAfternoon  IsEvening   IsNight
9131         -0.635634       1.110854    0.347037        -1.075849        -0.417095     0.631512               0.520170  ...   1.440974        0.378271   0.009013  -0.589643    -0.621745   1.767139 -0.341448
9132          1.573233      -0.253801   -1.077398         0.952832        -0.417095     0.080087              -0.075184  ...  -1.339223        0.019877   0.726720  -0.589643     1.608376  -0.565886 -0.341448
9134         -0.635634       0.974181    0.347037         0.952832        -0.417095    -0.288617              -0.297167  ...   1.440974       -0.753006   1.444428  -0.589643    -0.621745  -0.565886  2.928706
9135         -0.635634       0.071314    0.347037        -1.075849        -0.417095    -0.210308              -0.427730  ...  -1.339223       -0.319422   1.444428   1.695941    -0.621745  -0.565886 -0.341448
9141         -0.635634       0.684270    1.771471        -1.075849        -0.417095    -0.598589               0.374498  ...   1.440974       -0.576293  -0.708694  -0.589643    -0.621745  -0.565886 -0.341448
...                ...            ...         ...              ...              ...          ...                    ...  ...        ...             ...        ...        ...          ...        ...       ...
12420        -0.635634      -0.404969   -1.077398         0.952832        -0.417095     0.468369               0.126274  ...   1.440974        2.679965  -0.708694  -0.589643    -0.621745  -0.565886 -0.341448
12422         1.573233      -0.477447   -1.077398         0.952832        -0.417095     0.204076              -0.038170  ...   0.050876        4.399303  -0.708694  -0.589643     1.608376  -0.565886 -0.341448
12425        -0.635634      -2.436419    0.347037         0.952832        -0.417095     0.439003               1.665256  ...   0.050876        2.147099   0.009013  -0.589643    -0.621745  -0.565886 -0.341448
12426         1.573233       0.856146    0.347037         0.952832        -0.417095     0.295436               1.112967  ...   0.050876        4.219037   0.009013   1.695941    -0.621745  -0.565886 -0.341448
12427        -0.635634       0.856146    0.347037         0.952832        -0.417095     0.279122               0.459640  ...   1.440974        3.733602   0.009013  -0.589643     1.608376  -0.565886 -0.341448

[928 rows x 33 columns], 9131     1
9132     1
9134     1
9135     1
9141     1
        ..
12420    1
12422    1
12425    1
12426    1
12427    1
Name: Target_Multiclass, Length: 928, dtype: int32)
test_data : (       RSI_signal_EMA4  H8_Price_Move  H12_Bar_SW  Volume_Momentum  MA_Cross_50_100  Price_Range  ADX_14_x_RollingVol15  ...  H4_Bar_SW  RSI14_x_Volume  DayOfWeek  IsMorning  IsAfternoon  IsEvening   IsNight
12339        -0.635634       0.178996   -1.077398         0.952832        -0.417095     0.448791               0.325348  ...   0.050876        0.685415   1.444428   1.695941    -0.621745  -0.565886 -0.341448
12344        -0.635634       0.862358    0.347037         0.952832        -0.417095    -0.331034               0.282966  ...   0.050876        0.314579  -1.426401  -0.589643    -0.621745  -0.565886  2.928706
12353        -0.635634       0.400571    1.771471         0.952832        -0.417095    -0.122211              -0.397210  ...   1.440974        2.103264  -0.708694  -0.589643     1.608376  -0.565886 -0.341448
12355        -0.635634      -0.732154   -1.077398         0.952832        -0.417095    -0.497440              -0.041927  ...  -1.339223        0.738391   0.009013  -0.589643    -0.621745  -0.565886  2.928706
12362        -0.635634       2.017862    0.347037        -1.075849        -0.417095    -0.389766               2.758781  ...   1.440974        2.344423   0.726720  -0.589643    -0.621745   1.767139 -0.341448
...                ...            ...         ...              ...              ...          ...                    ...  ...        ...             ...        ...        ...          ...        ...       ...
16105        -0.635634       0.655279    0.347037        -1.075849        -0.417095    -0.637744              -0.570749  ...   0.050876        0.047690   0.726720   1.695941    -0.621745  -0.565886 -0.341448
16106        -0.635634       0.655279    0.347037         0.952832        -0.417095    -0.092845              -0.488848  ...   1.440974       -0.136875   0.726720  -0.589643     1.608376  -0.565886 -0.341448
16107        -0.635634      -0.802561    0.347037        -1.075849        -0.417095    -0.135262              -0.375458  ...  -1.339223       -0.114293   0.726720  -0.589643    -0.621745   1.767139 -0.341448
16109        -0.635634       0.502040   -1.077398         0.952832        -0.417095     0.200813              -0.292275  ...   1.440974        0.707668   1.444428  -0.589643    -0.621745  -0.565886  2.928706
16111        -0.635634      -0.152332   -1.077398        -1.075849        -0.417095    -0.301668               0.012836  ...  -1.339223       -0.225341   1.444428  -0.589643     1.608376  -0.565886 -0.341448

[928 rows x 33 columns], 12339    0
12344    0
12353    0
12355    0
12362    0
        ..
16105    0
16106    0
16107    1
16109    1
16111    0
Name: Target_Multiclass, Length: 928, dtype: int32)
df :        RSI_signal_EMA4  H8_Price_Move  H12_Bar_SW  Volume_Momentum  MA_Cross_50_100  Price_Range  ADX_14_x_RollingVol15  ...  H4_Bar_SW  RSI14_x_Volume  DayOfWeek  IsMorning  IsAfternoon  IsEvening  IsNight
12339               -1        0.00025        -1.0                1             -1.0      0.00441               0.039118  ...        0.0   179475.120571          4          1            0          0        0
12344               -1        0.00355         0.0                1             -1.0      0.00202               0.037745  ...        0.0   146750.144385          0          0            0          0        1
12353               -1        0.00132         1.0                1             -1.0      0.00266               0.015706  ...        1.0   304595.484878          1          0            1          0        0
12355               -1       -0.00415        -1.0                1             -1.0      0.00151               0.027218  ...       -1.0   184150.041609          2          0            0          0        1
12362               -1        0.00913         0.0               -1             -1.0      0.00184               0.117967  ...        1.0   325876.898442          3          0            0          1        0
...                ...            ...         ...              ...              ...          ...                    ...  ...        ...             ...        ...        ...          ...        ...      ...
16105               -1        0.00255         0.0               -1             -1.0      0.00108               0.010083  ...        0.0   123198.044948          3          1            0          0        0
16106               -1        0.00255         0.0                1             -1.0      0.00275               0.012737  ...        1.0   106910.839798          3          0            1          0        0
16107               -1       -0.00449         0.0               -1             -1.0      0.00262               0.016411  ...       -1.0   108903.633201          3          0            0          1        0
16109               -1        0.00181        -1.0                1             -1.0      0.00365               0.019106  ...        1.0   181438.878416          4          0            0          0        1
16111               -1       -0.00135        -1.0               -1             -1.0      0.00211               0.028992  ...       -1.0    99103.988493          4          0            1          0        0

[928 rows x 33 columns]
trade_df :                Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  D1_MACD_signal  RR_Ratio  Target Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0     2013-07-31 06:00:00      1.52238 2013-07-31 09:00:00     1.52238       Sell     0.0          2  ...             0.0       inf       0           0          -1            0                  1
1     2013-07-31 11:00:00      1.52118 2013-07-31 13:00:00     1.52118       Sell     0.0          2  ...             0.0       inf       0           0          -1            0                  1
2     2013-07-31 15:00:00      1.51504 2013-07-31 15:00:00     1.51504       Sell     0.0          2  ...             0.0       inf       0           0          -1            0                  1
3     2013-07-31 16:00:00      1.51342 2013-07-31 16:00:00     1.51602       Sell  -260.0          2  ...             0.0  2.003846       0           0          -1            0                  2
4     2013-07-31 19:00:00      1.51758 2013-07-31 19:00:00     1.51926       Sell  -168.0          2  ...             0.0  2.005952       0           0          -1            0                  2
...                   ...          ...                 ...         ...        ...     ...        ...  ...             ...       ...     ...         ...         ...          ...                ...
16107 2025-07-10 17:00:00      1.35522 2025-07-10 18:00:00     1.35522       Sell     0.0          3  ...            -1.0       inf       0           0          -1            0                  1
16108 2025-07-10 20:00:00      1.35633 2025-07-10 20:00:00     1.35747       Sell  -114.0          3  ...            -1.0  2.008772       0           0          -1            0                  2
16109 2025-07-11 04:00:00      1.35540 2025-07-11 10:00:00     1.35540       Sell     0.0          4  ...            -1.0       inf       0           0          -1            0                  1
16110 2025-07-11 12:00:00      1.35348 2025-07-11 12:00:00     1.35540       Sell  -192.0          4  ...            -1.0  2.005208       0           0          -1            0                  2
16111 2025-07-11 14:00:00      1.35289 2025-07-11 17:00:00     1.34874       Sell   415.0          4  ...            -1.0       inf       1           1          -1            1                  0

[16112 rows x 338 columns]
stats : {'buy': {'win_rate': 24.23, 'expectancy': -41.539}, 'sell': {'win_rate': 26.0, 'expectancy': -35.065}, 'buy_sell': {'win_rate': 25.09, 'expectancy': -38.398}}
features : 33
['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-07-31 ถึง 2020-09-15 (2604 วัน, 2787 records)
Val: 2020-05-13 ถึง 2022-10-12 (883 วัน, 928 records)
Test: 2022-09-16 ถึง 2025-07-11 (1030 วัน, 928 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 33

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              2161      13.41%
SL Hit              13824     85.80%
Technical Exit      127       0.79%
SL + Tech Exit      13951     86.59%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 382.86
ขาดทุนเฉลี่ยเมื่อ SL Hit: -83.20
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -80.57
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:4.60

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 13951
- อัตราส่วน: 86.59%
- กำไร/ขาดทุนเฉลี่ย: -83.18
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:4.60

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: 29.07
- ขาดทุนเฉลี่ยเมื่อชน SL: -95.25
- อัตราการชน TP: 11.81%
- อัตราการชน SL: 88.19%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count       mean      sum      mean
DayOfWeek                                     
Monday      3010 -24.975415 -75176.0  0.131229
Tuesday     3322 -16.962673 -56350.0  0.143287
Wednesday   3285 -24.884932 -81747.0  0.125723
Thursday    3336 -16.533273 -55155.0  0.138489
Friday      3159 -20.449826 -64601.0  0.131371

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count       mean      sum      mean
Hour                                     
0         5   0.000000      0.0  0.000000
3      1072 -25.010261 -26811.0  0.128731
4       711 -29.465541 -20950.0  0.123769
5       526 -13.982890  -7355.0  0.161597
6       435 -18.597701  -8090.0  0.131034
7       421 -27.871734 -11734.0  0.140143
8       549 -11.582878  -6359.0  0.169399
9       970 -25.275258 -24517.0  0.129897
10     1137 -11.552331 -13135.0  0.137203
11     1227 -18.938875 -23238.0  0.124694
12     1113 -15.392633 -17132.0  0.143756
13     1044 -19.150383 -19993.0  0.133142
14     1045 -11.129187 -11630.0  0.146411
15     1162 -12.494836 -14519.0  0.135972
16     1163 -11.947549 -13895.0  0.162511
17     1160 -39.523276 -45847.0  0.098276
18      940 -22.645745 -21287.0  0.131915
19      728 -30.532967 -22228.0  0.126374
20      703 -34.578947 -24309.0  0.109531
23        1   0.000000      0.0  0.000000
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GBPUSD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (928, 33), trade_df.shape = (16112, 338)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_09a_combined_df.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 33
['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW', 'Slope_EMA100', 'D1_Bar_longwick', 'Volume_Lag_50', 'H4_Bar_OSB', 'D1_Bar_TL', 'RSI_ROC_i4', 'Hour', 'MA_Cross_50_200', 'RSI_signal_EMA12', 'Bar_TL', 'H4_Price_Range', 'Bar_CL_OC', 'H4_Bar_longwick', 'Volume_Change_2', 'H8_MACD_signal', 'H4_MACD_deep', 'H4_Bar_SW', 'RSI14_x_Volume', 'DayOfWeek', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']

🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
🔍 ตรวจสอบคอลัมน์ DateTime ใน combined_df
   Columns ใน combined_df: ['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW', 'Slope_EMA100', 'D1_Bar_longwick', 'Volume_Lag_50', 'H4_Bar_OSB', 'D1_Bar_TL', 'RSI_ROC_i4', 'Hour', 'MA_Cross_50_200', 'RSI_signal_EMA12', 'Bar_TL', 'H4_Price_Range', 'Bar_CL_OC', 'H4_Bar_longwick', 'Volume_Change_2', 'H8_MACD_signal', 'H4_MACD_deep', 'H4_Bar_SW', 'RSI14_x_Volume', 'DayOfWeek', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
⚠️ ไม่พบคอลัมน์ 'DateTime' ใน combined_df - กำลังสร้างใหม่
🔄 ใช้ fallback method: สร้าง DateTime sequence
✅ สร้าง DateTime sequence จาก 2025-08-23 18:16:55.659698 ถึง 2025-10-01 09:16:55.659698
✅ แปลงและเรียงลำดับ DateTime สำเร็จ (ช่วงเวลา: 2025-08-23 18:16:55.659698 ถึง 2025-10-01 09:16:55.659698)
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 0 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_09b_combined_df_merge.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 44
['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW', 'Slope_EMA100', 'D1_Bar_longwick', 'Volume_Lag_50', 'H4_Bar_OSB', 'D1_Bar_TL', 'RSI_ROC_i4', 'Hour', 'MA_Cross_50_200', 'RSI_signal_EMA12', 'Bar_TL', 'H4_Price_Range', 'Bar_CL_OC', 'H4_Bar_longwick', 'Volume_Change_2', 'H8_MACD_signal', 'H4_MACD_deep', 'H4_Bar_SW', 'RSI14_x_Volume', 'DayOfWeek', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'DateTime', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 33
['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW']
✅ ใช้ Final selected feature: 33 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GBPUSD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW']
🔍 Debug: มีคอลัมน์ Close? False
🔍 Debug: มีคอลัมน์ EMA200? False
❌ ไม่พบคอลัมน์ที่จำเป็น: ['Close', 'High', 'Low', 'EMA200']
🔍 Debug: คอลัมน์ทั้งหมดใน df: ['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW', 'Slope_EMA100', 'D1_Bar_longwick', 'Volume_Lag_50', 'H4_Bar_OSB', 'D1_Bar_TL', 'RSI_ROC_i4', 'Hour', 'MA_Cross_50_200', 'RSI_signal_EMA12', 'Bar_TL', 'H4_Price_Range', 'Bar_CL_OC', 'H4_Bar_longwick', 'Volume_Change_2', 'H8_MACD_signal', 'H4_MACD_deep', 'H4_Bar_SW', 'RSI14_x_Volume', 'DayOfWeek', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'DateTime', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
❌ Multi-Model training ล้มเหลว - ลองใช้ Single-Model แทน
🔄 กำลังเปลี่ยนเป็น Single-Model Architecture...

🏗️ เปิดใช้งาน train and evaluate
✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น
   Train: 2787 samples, 33 features
   Val: 928 samples
   Test: 928 samples
🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test

⚙️ กำลัง Scaling Features...
✅ Scaling Features เสร็จสิ้น

🔍 การตรวจสอบ Data Quality และ Class Imbalance
============================================================
Train class distribution: Target_Multiclass
1    2135
0     652
Name: count, dtype: int64
Train class ratio (0:1): 0.31
Minority class ratio: 0.234
✅ Class distribution is acceptable

Data Quality Check:
- Train samples: 2787
- Val samples: 928
- Test samples: 928
- Features: 33
✅ No missing values detected
✅ No infinite values detected
⚙️ กำลังทำ SMOTE balancing...
SMOTE: ใช้ k_neighbors=5 (min_class_count=652)
SMOTE: ก่อน balance 0=652, 1=2135 | หลัง balance 0=2135, 1=2135

🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 2135, 0: 652}
   Imbalance ratio: 3.3:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
Class Ratio (0:1): 1.00:1
📁 สร้างโฟลเดอร์สำหรับ tuning files: LightGBM/Hyper_Multi/M60_GBPUSD

🔍 ตรวจสอบสถานะ Hyperparameter Tuning:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_best_params.json
   flag_file exists = False
   param_file exists = False

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV
🔄 เริ่ม RandomizedSearchCV...
   Training data: 4270 samples, 33 features
   Target distribution: {1: 2135, 0: 2135}
Fitting 5 folds for each of 100 candidates, totalling 500 fits
✅ RandomizedSearchCV เสร็จสิ้น
🎯 ผลลัพธ์ Hyperparameter Tuning:
==================================================
Best params: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 20, 'max_depth': 8, 'learning_rate': 0.02, 'feature_fraction': 0.7, 'bagging_freq': 5, 'bagging_fraction': 0.7}
Best AUC: nan

Top 5 parameter combinations:
AUC: nan (±nan) - {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 20, 'max_depth': 8, 'learning_rate': 0.02, 'feature_fraction': 0.7, 'bagging_freq': 5, 'bagging_fraction': 0.7}
AUC: nan (±nan) - {'reg_lambda': 0.0, 'reg_alpha': 0.02, 'num_leaves': 15, 'min_data_in_leaf': 20, 'max_depth': 8, 'learning_rate': 0.02, 'feature_fraction': 0.8, 'bagging_freq': 5, 'bagging_fraction': 0.7}
AUC: nan (±nan) - {'reg_lambda': 0.01, 'reg_alpha': 0.02, 'num_leaves': 50, 'min_data_in_leaf': 10, 'max_depth': 8, 'learning_rate': 0.05, 'feature_fraction': 0.7, 'bagging_freq': 2, 'bagging_fraction': 0.85}
AUC: nan (±nan) - {'reg_lambda': 0.01, 'reg_alpha': 0.01, 'num_leaves': 40, 'min_data_in_leaf': 20, 'max_depth': 6, 'learning_rate': 0.1, 'feature_fraction': 0.8, 'bagging_freq': 3, 'bagging_fraction': 0.8}
AUC: nan (±nan) - {'reg_lambda': 0.0, 'reg_alpha': 0.02, 'num_leaves': 20, 'min_data_in_leaf': 15, 'max_depth': 4, 'learning_rate': 0.02, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.7}

Overfitting Analysis:
Train Score: 1.0000
Test Score: nan
Gap: nan (✅ Good)
💾 บันทึก best hyperparameters ที่: LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_best_params.json
✅ ไฟล์ best_params ถูกสร้างแล้ว (ขนาด: 385 bytes)
🔒 ปิด tuning ในครั้งถัดไป (flag saved at: LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_tuning_flag.json)
✅ ไฟล์ flag ถูกสร้างแล้ว (ขนาด: 35 bytes)
✅ สร้างโมเดล LGBMClassifier ใหม่สำเร็จ
⚙️ กำลัง Fit โมเดลหลัก...
Training until validation scores don't improve for 200 rounds
[100]	valid_0's auc: 0.521398	valid_0's binary_logloss: 0.992083	valid_0's binary_error: 0.709052
[200]	valid_0's auc: 0.524147	valid_0's binary_logloss: 0.857603	valid_0's binary_error: 0.592672
Early stopping, best iteration is:
[44]	valid_0's auc: 0.531354	valid_0's binary_logloss: 1.16293	valid_0's binary_error: 0.764009
✅ ฝึกโมเดลหลักสำเร็จ

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GBPUSD M60:
  📊 Accuracy: 0.236 ❌ (min: 0.68)
  📈 AUC: 0.531 ❌ (min: 0.78)
  📋 Samples: 928 ✅ (min: 200)
  🎯 Overall: ❌ FAIL
  ⚠️ Model quality insufficient - consider:
    - Improve feature engineering or model parameters
    - Address class imbalance or add more discriminative features
⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ - อาจส่งผลต่อ win rate
   แนะนำ: ปรับ parameters, เพิ่มข้อมูล, หรือปรับปรุง features

🏗️ เปิดใช้งาน find best threshold on val with Trading Metrics
📊 Prepared features for threshold optimization: 33 available, 0 filled with 0
Threshold grid search with Trading Metrics (val set):
  Threshold | Win Rate | Expectancy | Trades | Sharpe | Max DD | PF | Kelly | Score
  -------------------------------------------------------------------------------------
⚠️ ไม่มี threshold ไหนที่มี trade >= 100 ใน validation set จะใช้ threshold default 0.35 แทน
🏗️ เปิดใช้งาน save optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_trend_following_threshold.json
   🎯 Threshold: 0.3500
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_counter_trend_threshold.json
   🎯 Threshold: 0.3500
-> เสร็จสิ้นการ Fit โมเดล LightGBM หลัก
-> กำลังเรียกตรวจสอบ features ของโมเดลหลัก
-> เสร็จสิ้นตรวจสอบ features

==================================================
  การทดสอบเปรียบเทียบกับ RandomForest 
==================================================
-> กำลังเรียก test random forest

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
              Feature  Importance
           ATR_ROC_i2    0.070368
      D1_Bar_longwick    0.069190
           RSI_ROC_i4    0.068359
        H8_Price_Move    0.065790
          Price_Range    0.065399
      Volume_Change_2    0.064459
      H4_Bar_longwick    0.064235
ADX_14_x_RollingVol15    0.063797
        Volume_Lag_50    0.063753
      Volume_Change_3    0.063545
       RSI14_x_Volume    0.062327
       H4_Price_Range    0.062073
                 Hour    0.032551
            DayOfWeek    0.025405
            H4_Bar_SW    0.015256

📈 RandomForest Performance:
              precision    recall  f1-score   support

           0       0.25      0.00      0.01       217
           1       0.77      1.00      0.87       711

    accuracy                           0.76       928
   macro avg       0.51      0.50      0.44       928
weighted avg       0.65      0.76      0.67       928

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GBPUSD_random_forest_feature_importance.csv
-> เสร็จสิ้น test random forest

📊 การกระจายของคลาส:
Train - 0: 652, 1: 2135
Test - 0: 217, 1: 711

🔍 เริ่มทำ Cross-Validation...
-> กำลังเรียก time series cv

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=619, total_samples=3715

📊 Fold 1/5:
  - Train size: 620 ตัวอย่าง (16.7% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7645161290322581, 0: 0.23548387096774193}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 474, 0: 146}
   Imbalance ratio: 3.2:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 3 trees)
  📊 ผลลัพธ์ Fold 1:
    - Accuracy:  0.2052
    - AUC:       0.5078
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 2/5:
  - Train size: 1239 ตัวอย่าง (33.4% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7796610169491526, 0: 0.22033898305084745}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 966, 0: 273}
   Imbalance ratio: 3.5:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 2:
    - Accuracy:  0.2294
    - AUC:       0.5075
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 3/5:
  - Train size: 1858 ตัวอย่าง (50.0% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7766415500538213, 0: 0.22335844994617868}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 1443, 0: 415}
   Imbalance ratio: 3.5:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.2375
    - AUC:       0.5526
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 4/5:
  - Train size: 2477 ตัวอย่าง (66.7% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7731126362535325, 0: 0.2268873637464675}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 1915, 0: 562}
   Imbalance ratio: 3.4:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.2456
    - AUC:       0.5224
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 5/5:
  - Train size: 3096 ตัวอย่าง (83.3% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7693798449612403, 0: 0.23062015503875968}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 2382, 0: 714}
   Imbalance ratio: 3.3:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.2504
    - AUC:       0.4961
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 สรุปการทำ Time Series CV:
   - จำนวน folds ที่วางแผน: 5
   - จำนวน folds ที่สำเร็จ: 5

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.2336
  - AUC:       0.5173
  - F1 Score:  0.0000
  - Precision: 0.0000
  - Recall:    0.0000
-> เสร็จสิ้น time series cv

  การประเมินผลโมเดลแบบละเอียด symbol GBPUSD timeframe M60

🏗️ เปิดใช้งาน enhanced evaluation
🔍 เริ่มการประเมินผลโมเดลแบบละเอียด symbol GBPUSD timeframe M60...
📁 สร้างโฟลเดอร์สำหรับ timeframe symbol GBPUSD timeframe M60 ที่: LightGBM/Multi/results\M60_GBPUSD
📊 Binary Classification

Classification Report (Binary Target):
              precision    recall  f1-score     support
0              0.234341  1.000000  0.379703  217.000000
1              1.000000  0.002813  0.005610  711.000000
accuracy       0.235991  0.235991  0.235991    0.235991
macro avg      0.617171  0.501406  0.192656  928.000000
weighted avg   0.820961  0.235991  0.093086  928.000000
📝 กำลังคำนวณ metrics...
📊 กำลังสร้าง visualization...
✅ บันทึกกราฟประสิทธิภาพที่: LightGBM/Multi/results\M60_GBPUSD\M60_GBPUSD_performance_curves.png
✅ บันทึกรายงานการประเมินที่: LightGBM/Multi/results\M60_GBPUSD\M60_GBPUSD_evaluation_report.csv
🎯 การประเมินผลสำหรับ symbol GBPUSD timeframe M60 เสร็จสมบูรณ์!

📊 ผลการประเมินแบบละเอียด symbol GBPUSD timeframe M60
AUC-ROC: 0.5465
AUC-PR: 0.7993
              precision    recall  f1-score     support
0              0.234341  1.000000  0.379703  217.000000
1              1.000000  0.002813  0.005610  711.000000
accuracy       0.235991  0.235991  0.235991    0.235991
macro avg      0.617171  0.501406  0.192656  928.000000
weighted avg   0.820961  0.235991  0.093086  928.000000

📌 สรุปผลลัพธ์แบบละเอียด:
- Timeframe: M60
- Accuracy: 0.2360
- Auc_roc: 0.5465
- Auc_pr: 0.7993
- F1: 0.1927
- Precision: 0.6172
- Recall: 0.5014

กำลังบันทึกโมเดลที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_trained.pkl
✅ บันทึกโมเดลเรียบร้อย (ขนาด: 163.81 KB)

กำลังบันทึก features ที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_features.pkl
✅ บันทึก features เรียบร้อย (จำนวน features: 33)

กำลังบันทึก Scaler ที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_scaler.pkl
✅ บันทึก Scaler เรียบร้อย (ขนาด: 2.25 KB)

📝 บันทึกประวัติการเทรนที่: LightGBM/Multi/training_history\M60_GBPUSD_training_history.csv
-> กำลังสร้าง Feature Importance
✅ พิมพ์ feature ก่อนส่งเข้า plot feature importance
['RSI_signal_EMA4', 'H8_Price_Move', 'H12_Bar_SW', 'Volume_Momentum', 'MA_Cross_50_100', 'Price_Range', 'ADX_14_x_RollingVol15', 'Volume_Change_3', 'ATR_ROC_i2', 'Bar_SW', 'Slope_EMA100', 'D1_Bar_longwick', 'Volume_Lag_50', 'H4_Bar_OSB', 'D1_Bar_TL', 'RSI_ROC_i4', 'Hour', 'MA_Cross_50_200', 'RSI_signal_EMA12', 'Bar_TL', 'H4_Price_Range', 'Bar_CL_OC', 'H4_Bar_longwick', 'Volume_Change_2', 'H8_MACD_signal', 'H4_MACD_deep', 'H4_Bar_SW', 'RSI14_x_Volume', 'DayOfWeek', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ LightGBM_Single symbol GBPUSD timeframe M60

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
              Feature   Gain  Split
            DayOfWeek 0.1992 0.1113
            H4_Bar_SW 0.0932 0.0682
           ATR_ROC_i2 0.0500 0.0572
       H8_MACD_signal 0.0478 0.0290
       RSI14_x_Volume 0.0468 0.0478
                 Hour 0.0438 0.0549
           RSI_ROC_i4 0.0425 0.0533
        H8_Price_Move 0.0416 0.0572
      H4_Bar_longwick 0.0403 0.0509
           H12_Bar_SW 0.0391 0.0337
ADX_14_x_RollingVol15 0.0385 0.0486
          Price_Range 0.0377 0.0431
      Volume_Change_2 0.0347 0.0462
       H4_Price_Range 0.0328 0.0431
      Volume_Change_3 0.0306 0.0368

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GBPUSD_feature_importance.csv (ขนาด: 1837 bytes)
-> เสร็จสิ้น Feature Importance

🔍 Top 5 Most Important Features (Gain):
DayOfWeek: 0.1992 (Gain), 0.1113 (Split)
H4_Bar_SW: 0.0932 (Gain), 0.0682 (Split)
ATR_ROC_i2: 0.0500 (Gain), 0.0572 (Split)
H8_MACD_signal: 0.0478 (Gain), 0.0290 (Split)
RSI14_x_Volume: 0.0468 (Gain), 0.0478 (Split)

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance_comparison.csv

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.2336    | 0.2360 |
| AUC         | 0.5173    | 0.5465 |
| F1 Score    | 0.0000    | 0.1927 |
-> train and evaluate ทำงานเสร็จสิ้น

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GBPUSD M60:
  📊 Accuracy: 0.236 ❌ (min: 0.68)
  📈 AUC: 0.531 ❌ (min: 0.78)
  📋 Samples: 928 ✅ (min: 200)
  🎯 Overall: ❌ FAIL
  ⚠️ Model quality insufficient - consider:
    - Improve feature engineering or model parameters
    - Address class imbalance or add more discriminative features
⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ แต่จะคืนค่าผลลัพธ์ต่อไป
✅ คืนค่าผลลัพธ์สำเร็จ: model=LGBMClassifier, scaler=StandardScaler

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Single-Model
🔍 Debug: trade_df is not None: True
🔍 Debug: trade_df.empty: False
🔍 Debug: trade_df length: 16112
✅ กำลังบันทึกสรุปสำหรับ Single-Model
   📊 Test stats: {'buy': {'count': 8364, 'win_rate': 12.94, 'expectancy': -22.18}, 'sell': {'count': 7748, 'win_rate': 14.12, 'expectancy': -19.04}, 'buy_sell': {'count': 16112, 'win_rate': 13.51, 'expectancy': -20.67}}
   📊 Train+Val stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'buy_sell': {'count': 928, 'win_rate': 0.0, 'expectancy': 0.0}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 33, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GBPUSD, timeframe=M60, scenario=None
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'buy_sell': {'count': 928, 'win_rate': 0.0, 'expectancy': 0.0}}
🔍 Debug: test_stats={'buy': {'count': 8364, 'win_rate': 12.94, 'expectancy': -22.18}, 'sell': {'count': 7748, 'win_rate': 14.12, 'expectancy': -19.04}, 'buy_sell': {'count': 16112, 'win_rate': 13.51, 'expectancy': -20.67}}
🔍 Debug: model_metrics={'timeframe': 'M60', 'accuracy': 0.23599137931034483, 'auc': 0.5464880385256049, 'f1': 0.1926563176797851, 'precision': 0.617170626349892, 'recall': 0.5014064697609001, 'confusion_matrix': array([[217,   0],
       [709,   2]], dtype=int64), 'auc_pr': 0.7992778780529594}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 33, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 56.7/100
📈 Win Rate เฉลี่ย: 38.3%, Expectancy เฉลี่ย: -7.82
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 80.1)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GBPUSD MM60 เรียบร้อย
📊 Performance Score: 33.18
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ
   - Current Entry Config: config_1_enhanced_signal
   - Symbol: GBPUSD, Timeframe: M60
   - Test stats keys: ['buy', 'sell', 'buy_sell']
   - Buy_sell stats: {'count': 16112, 'win_rate': 13.51, 'expectancy': -20.67}
   - Performance data prepared: {'symbol': 'GBPUSD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.1351, 'expectancy': -20.67, 'profit_factor': 0, 'num_trades': 16112, 'max_drawdown': 0, 'model_accuracy': 0.23599137931034483, 'model_auc': 0.5464880385256049, 'model_f1': 0.1926563176797851, 'training_date': '2025-10-01T09:26:16.817883', 'num_features': 33}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GBPUSD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GBPUSD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD\performance_summary.json
   📁 ขนาดไฟล์: 695 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น
✅ บันทึกสรุปสำหรับ Single-Model เรียบร้อย
✅ Single-Model fallback สำเร็จ

🎯 ทดสอบ Optimal Parameters สำหรับ Single-Model...
⚠️ ใช้ส่วนท้ายของ combined_df สำหรับ Single-Model validation
🎯 ทดสอบ Optimal Threshold...

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GBPUSD MM60, ใช้ค่า default: 0.25
🏗️ เปิดใช้งาน save optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_trend_following_threshold.json
   🎯 Threshold: 0.2500
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_counter_trend_threshold.json
   🎯 Threshold: 0.2500
✅ Optimal Threshold: 0.2500
🎯 ทดสอบ Optimal nBars SL...

🏗️ เปิดใช้งาน find optimal nbars sl
⚠️ DataFrame ที่ส่งเข้าไม่มีคอลัมน์ 'Low' หรือ 'High'

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GBPUSD MM60, ใช้ค่า default: 4
✅ Optimal nBars SL: 4

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 33
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate: 33
[INFO] รายชื่อ Features หลัง train (แสดง 10 ตัวแรก): [28, 26, 8, 24, 27, 16, 15, 1, 22, 2]
[INFO] ... และอีก 23 features
🔍 Debug ไฟล์ CSV_Files_Fixed/GBPUSD_H1_FIXED.csv:
   metrics: {'timeframe': 'M60', 'accuracy': 0.23599137931034483, 'auc': 0.5464880385256049, 'f1': 0.1926563176797851, 'precision': 0.617170626349892, 'recall': 0.5014064697609001, 'confusion_matrix': array([[217,   0],
       [709,   2]], dtype=int64), 'auc_pr': 0.7992778780529594}
   cv_results: {'accuracy': 0.23360258481421647, 'auc': 0.517270659141926, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.2336    | 0.2360 |
| AUC         | 0.5173    | 0.5465 |
| F1 Score    | 0.0000    | 0.1927 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'timeframe': 'M60', 'accuracy': 0.23599137931034483, 'auc': 0.5464880385256049, 'f1': 0.1926563176797851, 'precision': 0.617170626349892, 'recall': 0.5014064697609001, 'confusion_matrix': array([[217,   0],
       [709,   2]], dtype=int64), 'auc_pr': 0.7992778780529594}
🔍 Debug cv_results from result_dict: {'accuracy': 0.23360258481421647, 'auc': 0.517270659141926, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Final model_metrics: {'avg_accuracy': 0.23599137931034483, 'avg_f1_score': 0.1926563176797851, 'avg_auc': 0.5464880385256049, 'total_train_samples': 2787, 'total_test_samples': 928}
⚠️ เกิดข้อผิดพลาดในการโหลด thresholds: argument of type 'float' is not iterable

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_GBPUSD_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_GBPUSD)
🔍 Debug loaded time_filters: {'days': [3, 1, 4], 'hours': [19, 20, 16, 5, 8, 18, 12, 13], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 79.02721088435109, 'total_trades': 147.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Tuesday': {'win_rate': 0.0, 'expectancy': 98.76190476190149, 'total_trades': 189.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Wednesday': {'win_rate': 0.0, 'expectancy': 57.55932203389653, 'total_trades': 177.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Thursday': {'win_rate': 0.0, 'expectancy': 112.00497512437558, 'total_trades': 201.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Friday': {'win_rate': 0.0, 'expectancy': 87.3691588785022, 'total_trades': 214.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}}, 'hours': {3: {'win_rate': 0.0, 'expectancy': 68.96226415094102, 'total_trades': 53.0, 'strong_buy_rate': 0.0}, 4: {'win_rate': 0.0, 'expectancy': 55.347826086955145, 'total_trades': 46.0, 'strong_buy_rate': 0.0}, 5: {'win_rate': 0.0, 'expectancy': 122.02941176470073, 'total_trades': 34.0, 'strong_buy_rate': 0.0}, 6: {'win_rate': 0.0, 'expectancy': 72.14285714285316, 'total_trades': 28.0, 'strong_buy_rate': 0.0}, 7: {'win_rate': 0.0, 'expectancy': 25.17647058823304, 'total_trades': 17.0, 'strong_buy_rate': 0.0}, 8: {'win_rate': 0.0, 'expectancy': 107.89999999999559, 'total_trades': 30.0, 'strong_buy_rate': 0.0}, 9: {'win_rate': 0.0, 'expectancy': 69.40350877192795, 'total_trades': 57.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': 78.97183098591213, 'total_trades': 71.0, 'strong_buy_rate': 0.0}, 11: {'win_rate': 0.0, 'expectancy': 67.92647058823326, 'total_trades': 68.0, 'strong_buy_rate': 0.0}, 12: {'win_rate': 0.0, 'expectancy': 96.18867924528055, 'total_trades': 53.0, 'strong_buy_rate': 0.0}, 13: {'win_rate': 0.0, 'expectancy': 84.3728813559287, 'total_trades': 59.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 82.86206896551454, 'total_trades': 58.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': 64.1944444444431, 'total_trades': 72.0, 'strong_buy_rate': 0.0}, 16: {'win_rate': 0.0, 'expectancy': 129.33333333333061, 'total_trades': 75.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': 77.45783132529954, 'total_trades': 83.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': 105.8260869565204, 'total_trades': 46.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 144.27777777777433, 'total_trades': 36.0, 'strong_buy_rate': 0.0}, 20: {'win_rate': 0.0, 'expectancy': 137.35714285714025, 'total_trades': 42.0, 'strong_buy_rate': 0.0}}}, 'adaptive_settings': {'min_win_rate': 0.3, 'min_expectancy': 0.0, 'adaptive_threshold': True, 'confidence_level': 0.8}, 'time_blocks': ['12:00-13:59', '18:00-20:59'], 'performance_comparison': {'filtered_vs_all': {'win_rate_improvement': 0.0, 'expectancy_improvement': 41.420705424725796, 'filtered_trades': 246, 'total_trades': 928, 'coverage': 0.2650862068965517, 'filtered_win_rate': 0.0, 'all_win_rate': 0.0}, 'best_day': {'win_rate': 0.0, 'total': 147.0, 'expectancy': 79.02721088435109, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0, 'combined_score': 31.61088435374044}, 'best_hour': {'win_rate': 0.0, 'total': 53.0, 'expectancy': 68.96226415094102, 'strong_buy_rate': 0.0, 'combined_score': 27.58490566037641}}}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: GBPUSD, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (16112, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Buy': 8364, 'Sell': 7748}
🔍 calculate_trade_metrics: input shape = (8364, 338)
   📊 Total trades: 8364
   💰 Winning trades: 1082/8364
   💰 Total profit: -185512.00
   ✅ Calculated metrics: {'count': 8364, 'win_rate': 12.93639406982305, 'expectancy': -22.17981826876972, 'trade_accuracy': 0.1293639406982305, 'trade_f1_score': 0.15523672883787662, 'trade_auc': 0.5517455762792922}
🔍 calculate_trade_metrics: input shape = (7748, 338)
   📊 Total trades: 7748
   💰 Winning trades: 1094/7748
   💰 Total profit: -147517.00
   ✅ Calculated metrics: {'count': 7748, 'win_rate': 14.119772844605057, 'expectancy': -19.03936499742442, 'trade_accuracy': 0.14119772844605058, 'trade_f1_score': 0.1694372741352607, 'trade_auc': 0.5564790913784202}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: GBPUSD_M60
   Current F1: 0.1926563176797851
   Current AUC: 0.5464880385256049
✅ No previous data for comparison
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: GBPUSD, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.13505461767626614, 'expectancy': -20.669625124133216, 'profit_factor': 0.7131088368566815, 'count': 16112, 'max_drawdown': inf}
   - Performance data prepared: {'symbol': 'GBPUSD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.13505461767626614, 'expectancy': -20.669625124133216, 'profit_factor': 0.7131088368566815, 'num_trades': 16112, 'max_drawdown': inf, 'model_accuracy': 0.23599137931034483, 'model_auc': 0.5464880385256049, 'model_f1': 0.1926563176797851, 'training_date': '2025-10-01T09:26:16.993845', 'training_type': 'unknown', 'num_features': 33}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GBPUSD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GBPUSD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD\performance_summary.json
   📁 ขนาดไฟล์: 776 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)

✅ เสร็จสิ้นการประมวลผลกลุ่ม M60
📊 ประมวลผล: 1 ไฟล์
📈 ผลลัพธ์: 1 รายการ

📊 ไม่มีการปรับ threshold ในรอบนี้
🔍 Debug: round_results = <class 'dict'>
✅ กลุ่ม M60 สำเร็จ
📊 ผลลัพธ์กลุ่ม M60: สำเร็จ 1, ผิดพลาด 0
⏱️ เวลาที่ใช้: 956.9 วินาที (15.9 นาที)
📈 เฉลี่ยต่อไฟล์: 956.9 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 956.9 วินาที (15.9 นาที)
   ✅ ไฟล์สำเร็จ: 1
   ❌ ไฟล์ผิดพลาด: 0
   ⏰ เวลาสิ้นสุด: 09:26:17
   📊 M60: 956.9s (956.9s/ไฟล์)

============================================================
⏭️ ข้ามการวิเคราะห์ Feature Importance ข้าม Assets
============================================================
💡 หมายเหตุ: การวิเคราะห์จะทำงานเฉพาะเมื่อเทรนโมเดลใหม่และมีผลลัพธ์
============================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-10-01 09:10:20
⏰ เวลาสิ้นสุด: 2025-10-01 09:26:17
⏱️ เวลาที่ใช้ทั้งหมด: 956.9 วินาที (15.9 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 4 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว

================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================

================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินทั้งหมด
================================================================================
2025-10-01 09:26:17 | INFO     | FinancialAnalysis | 🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์ | account_balance=1000
📊 พบข้อมูลการเทรด: 16112 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png
💾 บันทึกการวิเคราะห์สมบูรณ์:
   📄 Summary: Financial_Analysis_Results/complete_financial_analysis.json
   📊 Risk Table: Financial_Analysis_Results/risk_management_table.csv
   📝 Report: Financial_Analysis_Results/financial_analysis_report.txt

🎨 สร้างกราฟ Financial Analysis แบบแยกรายละเอียด
📊 พบ timeframes: ['M60']
📊 พบ symbols: ['GBPUSD']
✅ บันทึกกราฟ M60 ที่: Financial_Analysis_Results\trading_performance_M60.png
✅ บันทึกไฟล์ CSV M60 ที่: Financial_Analysis_Results\trading_summary_M60.csv
🎉 สร้างกราฟ Financial Analysis แบบแยกรายละเอียดเสร็จสิ้น
2025-10-01 09:26:35 | INFO     | FinancialAnalysis | ✅ การวิเคราะห์ทางการเงินเสร็จสมบูรณ์ | account_balance=1000 | total_trades=16112 | total_profit_usd=-333029.*********** | max_drawdown_usd=334414.*********** | recommended_lot_size=5.980610859592694e-05 | max_risk_percentage=2.0

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 16112
💵 Total Profit (1.0 lot): $-333,029.00
📉 Max Drawdown (1.0 lot): $334,414.00
🎯 Recommended Lot Size: 0.0001
⚠️ Max Risk: 2.00%
============================================================

🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
📁 ผลลัพธ์บันทึกที่: Financial_Analysis_Results

📈 สรุปผลการวิเคราะห์:
   💰 ยอดเงินในบัญชี: $1,000.00
   📊 จำนวนการเทรดทั้งหมด: 16112
   💵 กำไรรวม (1.0 lot): $-333,029.00
   📉 Drawdown สูงสุด (1.0 lot): $334,414.00
   🎯 ขนาดล็อตที่แนะนำ: 0.0001
   ⚠️ ความเสี่ยงสูงสุด: 2.00%

🎨 สร้างกราฟแยกรายละเอียดเพิ่มเติม...
⚠️ ไม่สามารถสร้างกราฟแยกรายละเอียดเพิ่มเติม: name 'create_enhanced_financial_charts' is not defined
🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
