timestamp,scenario,architecture,train_buy_count,train_buy_win_rate,train_buy_expectancy,train_sell_count,train_sell_win_rate,train_sell_expectancy,train_total_count,train_total_win_rate,train_total_expectancy,test_buy_count,test_buy_win_rate,test_buy_expectancy,test_sell_count,test_sell_win_rate,test_sell_expectancy,test_total_count,test_total_win_rate,test_total_expectancy,accuracy,auc,f1,precision,recall,threshold,nbars_sl,num_features,hyperparameter_tuning,use_smote,performance_score
2025-10-01 09:26:16,single_model,multi_model,0,0.0,0.0,0,0.0,0.0,928,0.0,0.0,8364,12.94,-22.18,7748,14.12,-19.04,16112,13.51,-20.67,0.23599137931034483,0.5464880385256049,0.1926563176797851,0.617170626349892,0.5014064697609001,0.5,5,33,False,False,33.182943338841376
